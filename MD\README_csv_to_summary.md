# CSV to Summary 命令列工具使用說明書

## 概述

`csv_to_summary.py` 是一個命令列工具，用於批量處理 CSV 檔案並生成 FT Summary 和 EQC Summary。此工具重用現有的 `BatchCsvToExcelProcessor` 核心處理邏輯，支援兩種處理模式，並新增支援壓縮檔輸入功能。

## 系統要求

### Python 檔案依賴

確保以下 Python 檔案存在於專案目錄中：

```
專案目錄/
├── csv_to_summary.py                              # 主執行檔案
├── batch_csv_to_excel_processor.py               # 核心批量處理器
└── src/
    └── infrastructure/
        └── adapters/
            └── excel/
                ├── csv_to_excel_converter.py      # CSV 轉 Excel 轉換器
                ├── ft_summary_generator.py        # FT Summary 生成器
                ├── site_column_finder.py          # Site 欄位查找器
                ├── strategy_b_processor.py        # 策略 B 處理器
                ├── summary_generator.py           # Summary 生成器
                ├── ft_summary_converter.py        # FT Summary 轉換器
                ├── advanced_performance_manager.py # 性能管理器
                └── cta/
                    └── cta_integrated_processor.py # CTA 整合處理器
```

### Python 套件依賴

安裝必要的 Python 套件：

```bash
pip install pandas numpy openpyxl xlsxwriter python-dotenv pytz
```

**壓縮檔支援（可選）**：
```bash
pip install rarfile py7zr  # 支援 RAR 和 7Z 格式
```

或使用虛擬環境：

```bash
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

pip install pandas numpy openpyxl xlsxwriter python-dotenv pytz rarfile py7zr
```

## 環境變數設定 (.env)

在專案根目錄創建 `.env` 檔案，並設定以下變數：

```env
# BIN1 保護機制
BIN1_PROTECTION=true

# 檔案過濾關鍵字
EXCLUDED_FILE_KEYWORDS=eqctotaldata,eqcfaildata,summary,correlation

# FT 處理關鍵字
FT_PROCESSING_KEYWORDS=auto_qc,ft,final_test

# QC 處理關鍵字  
QC_PROCESSING_KEYWORDS=qc,quality_control,eqc

# AI 提供者設定 (可選)
AI_PROVIDER=ollama
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EXPECTED_VECTOR_DIMENSION=384
```

## 使用方法

### 基本語法

```bash
python3 csv_to_summary.py <folder_path|archive_path> [options]
```

### 參數說明

| 參數 | 說明 |
|------|------|
| `folder_path` | CSV 檔案所在的資料夾路徑或壓縮檔路徑（必填） |
| `--excel`, `--with-excel` | 同時產生 Excel 檔案（預設只產生 Summary） |
| `--verbose`, `-v` | 詳細輸出模式 |
| `--version` | 顯示版本資訊 |
| `--help`, `-h` | 顯示說明 |

### 支援的壓縮格式

- **ZIP** (.zip) - 標準 ZIP 壓縮檔
- **7Z** (.7z) - 7-Zip 壓縮檔（需要 py7zr 套件）
- **RAR** (.rar) - WinRAR 壓縮檔（需要 rarfile 套件）
- **TAR** (.tar, .tar.gz, .tgz, .tar.bz2) - TAR 壓縮檔

### 處理模式

#### 1. 快速模式（預設）
僅產生 Summary CSV 檔案，處理速度較快：

```bash
python3 csv_to_summary.py doc/20250523
```

**輸出檔案：**
- `FT_SUMMARY.csv` - FT 檔案整合摘要
- `EQC_SUMMARY.csv` - EQC 檔案整合摘要
- 各個檔案的 `*_summary.csv`

#### 2. 完整模式
產生 Excel 和 Summary 檔案：

```bash
python3 csv_to_summary.py doc/20250523 --excel
```

**輸出檔案：**
- `FT_SUMMARY.xlsx` - FT 檔案整合摘要（Excel 格式）
- `EQC_SUMMARY.xlsx` - EQC 檔案整合摘要（Excel 格式）
- `FT_SUMMARY.csv` - FT 檔案整合摘要（CSV 格式）
- `EQC_SUMMARY.csv` - EQC 檔案整合摘要（CSV 格式）
- 各個檔案的 `*.xlsx` 和 `*_summary.csv`

#### 3. 詳細輸出模式
顯示詳細的處理過程：

```bash
python3 csv_to_summary.py doc/20250523 --excel --verbose
```

## 使用範例

### 範例 1：基本處理
```bash
# 僅產生 Summary（快速模式）
python3 csv_to_summary.py /path/to/csv/folder
```

### 範例 2：完整處理
```bash
# 產生 Excel + Summary（完整模式）
python3 csv_to_summary.py /path/to/csv/folder --excel
```

### 範例 3：詳細輸出
```bash
# 完整模式 + 詳細輸出
python3 csv_to_summary.py /path/to/csv/folder --excel --verbose
```

### 範例 4：使用相對路徑
```bash
# 處理當前目錄下的 data 資料夾
python3 csv_to_summary.py ./data --excel
```

### 範例 5：Windows 路徑
```bash
# Windows 系統路徑
python3 csv_to_summary.py "C:\Data\CSV Files" --excel --verbose
```

### 範例 6：壓縮檔處理
```bash
# 處理 ZIP 壓縮檔
python3 csv_to_summary.py data.zip --excel

# 處理 7Z 壓縮檔
python3 csv_to_summary.py archive.7z --verbose

# 處理 RAR 壓縮檔
python3 csv_to_summary.py files.rar --excel --verbose

# 處理 TAR 壓縮檔
python3 csv_to_summary.py backup.tar.gz
```

**壓縮檔處理流程：**
1. 自動偵測壓縮檔格式
2. 解壓縮到同目錄下的同名資料夾
3. 遞迴掃描並解壓縮資料夾內的其他壓縮檔
4. 刪除原始壓縮檔
5. 執行 CSV 處理流程

## 輸出結果說明

### 處理統計資訊
工具會顯示以下統計資訊：
- 總檔案數
- 成功處理數
- 跳過檔案數
- 失敗檔案數
- 處理時間

### FT Summary 資訊
- 生成檔案數
- 整合檔案路徑
- 處理的 FT 檔案清單

### EQC Summary 資訊
- EQC 檔案數
- EQC 摘要檔案路徑
- 全通過檔案路徑

## 錯誤處理

### 常見錯誤及解決方法

#### 1. 匯入錯誤
```
[ERROR] 錯誤: 無法匯入核心處理器
```
**解決方法：**
- 確認 `batch_csv_to_excel_processor.py` 檔案存在
- 檢查所有依賴的 Python 檔案是否完整
- 安裝必要的 Python 套件

#### 2. 資料夾不存在
```
[ERROR] 錯誤: 資料夾不存在 'path/to/folder'
```
**解決方法：**
- 檢查路徑是否正確
- 確認資料夾確實存在
- 使用絕對路徑或正確的相對路徑

#### 3. 沒有 CSV 檔案
```
[WARNING] 警告: 資料夾中沒有找到 CSV 檔案
```
**解決方法：**
- 確認資料夾中有 CSV 檔案
- 檢查檔案副檔名是否為 `.csv`

#### 4. 權限問題
```
[ERROR] 權限錯誤
```
**解決方法：**
- 確認對資料夾有讀取權限
- 確認對輸出位置有寫入權限

## 性能最佳化

### 處理大量檔案的建議

1. **使用快速模式**：如果只需要 Summary，使用預設模式可節省處理時間

2. **分批處理**：對於大量檔案，可以分批處理
   ```bash
   python3 csv_to_summary.py folder1 --excel
   python3 csv_to_summary.py folder2 --excel
   ```

3. **使用 SSD**：將檔案放在 SSD 上可提升處理速度

4. **足夠記憶體**：確保系統有足夠記憶體處理大型檔案

## 與網頁版本的差異

| 功能 | 網頁版本 | 命令列版本 |
|------|---------|-----------|
| 使用方式 | 瀏覽器操作 | 命令列執行 |
| 批次處理 | 支援 | 支援 |
| 進度顯示 | 圖形界面 | 文字輸出 |
| 自動化 | 手動觸發 | 可腳本化 |
| 結果下載 | 網頁下載 | 直接生成於資料夾 |

## 自動化腳本範例

### Bash 腳本
```bash
#!/bin/bash
# 批量處理多個資料夾

folders=("data/batch1" "data/batch2" "data/batch3")

for folder in "${folders[@]}"; do
    echo "處理資料夾: $folder"
    python3 csv_to_summary.py "$folder" --excel --verbose
    echo "完成: $folder"
    echo "---"
done
```

### Python 腳本
```python
#!/usr/bin/env python3
import subprocess
import os

folders = ["data/batch1", "data/batch2", "data/batch3"]

for folder in folders:
    if os.path.exists(folder):
        print(f"處理資料夾: {folder}")
        result = subprocess.run([
            "python3", "csv_to_summary.py", 
            folder, "--excel", "--verbose"
        ])
        if result.returncode == 0:
            print(f"[OK] 成功: {folder}")
        else:
            print(f"[ERROR] 失敗: {folder}")
    else:
        print(f"[WARNING] 資料夾不存在: {folder}")
```

## 技術架構

### 核心組件
- **csv_to_summary.py**: 命令列介面和參數解析
- **BatchCsvToExcelProcessor**: 核心批量處理邏輯
- **CsvToExcelConverter**: CSV 到 Excel 轉換
- **FTSummaryGenerator**: FT Summary 生成
- **CTAIntegratedProcessor**: CTA 前置處理

### 處理流程
1. CTA 前置處理
2. CSV 檔案掃描和分類
3. MD5 去重複
4. 並行處理（FT/EQC）
5. Summary 整合
6. 結果輸出

## 版本資訊

- **版本**: v1.0.0
- **相容性**: Python 3.7+
- **依賴**: pandas, numpy, openpyxl, xlsxwriter, python-dotenv, pytz

## 授權與支援

此工具為內部專案工具，如有問題請聯繫開發團隊。

---

**最後更新**: 2025-07-11
**作者**: AI Assistant
**相關檔案**: csv_to_summary.py, batch_csv_to_excel_processor.py