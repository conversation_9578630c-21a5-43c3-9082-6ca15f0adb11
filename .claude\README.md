# Claude Hooks 系統

強大的代碼分析、重構和優化工具，專為提高代碼品質和維護性而設計。

## 🚀 主要功能

### 1. 智能檔案大小管理
- **自動檢測超過 500 行的檔案**
- **智能結構化拆分**：按類別、功能或混合策略拆分
- **保持代碼完整性**：不會減少功能，只重新組織結構
- **多語言支援**：Python、JavaScript、TypeScript、Java 等

### 2. 重複代碼檢測與清理
- **AST 級別分析**：準確檢測功能相同的代碼
- **保留最新版本**：自動選擇最新的代碼版本
- **安全清理**：提供備份和回滾機制
- **智能合併**：可將相似代碼提取為共用函數

### 3. Import 優化
- **檢測未使用的導入**：自動清理冗餘導入
- **發現重複實現**：建議使用現有模組而非重寫
- **循環依賴檢測**：識別並建議解決方案
- **標準庫建議**：推薦使用標準庫替代第三方庫

### 4. 代碼品質檢查
- **複雜度分析**：圈複雜度、嵌套深度檢查
- **安全問題檢測**：識別常見安全風險
- **最佳實踐檢查**：文檔字符串、命名規範等
- **品質評分**：提供 0-100 的代碼品質分數

### 5. 測試驗證與回滾
- **語法檢查**：確保代碼語法正確
- **自動測試**：運行測試套件驗證功能
- **快照系統**：變更前自動創建備份
- **一鍵回滾**：快速恢復到任何歷史狀態

## 📁 目錄結構

```
.claude/
├── hooks/                      # 核心功能模組
│   ├── main.py                # 主控制器
│   ├── config_manager.py      # 配置管理
│   ├── validator.py           # 驗證和回滾
│   ├── analyzers/             # 分析器模組
│   │   ├── file_size_analyzer.py
│   │   ├── duplicate_detector.py
│   │   ├── import_analyzer.py
│   │   └── quality_checker.py
│   └── refactors/             # 重構器模組
│       ├── file_splitter.py
│       ├── duplicate_cleaner.py
│       └── import_optimizer.py
├── config.yaml                # 主配置檔案
├── user_config.yaml          # 用戶自定義配置
├── snapshots/                 # 代碼快照備份
├── backups/                   # 操作備份
└── run_hooks.py               # 啟動腳本
```

## 🛠️ 快速開始

### 1. 初始化系統

```bash
# 進入專案根目錄
cd /path/to/your/project

# 初始化 Claude Hooks
python .claude/run_hooks.py --init
```

### 2. 基本使用

```bash
# 完整專案分析
python .claude/run_hooks.py --analyze

# 分析特定檔案
python .claude/run_hooks.py --analyze --files src/main.py src/utils.py

# 自動拆分超大檔案
python .claude/run_hooks.py --split

# 清理重複代碼
python .claude/run_hooks.py --clean

# 優化導入語句
python .claude/run_hooks.py --optimize

# 驗證專案狀態
python .claude/run_hooks.py --validate

# 乾跑模式（僅顯示建議，不執行）
python .claude/run_hooks.py --analyze --dry-run
```

### 3. 配置自定義

編輯 `.claude/user_config.yaml` 來自定義設定：

```yaml
# 調整檔案大小限制
file_size_analysis:
  max_lines: 600  # 預設 500

# 自定義測試命令
validation:
  test_command: "pytest tests/ -v --tb=short"

# 調整品質檢查
quality_checking:
  max_cyclomatic_complexity: 8  # 預設 10
```

## 🔧 詳細功能說明

### 檔案拆分策略

1. **按類別拆分**：每個大型類別獨立成檔案
2. **按功能拆分**：依函數前綴分組（如 `parse_*`, `validate_*`）
3. **混合拆分**：結合類別和函數的智能拆分

### 重複代碼處理

- **完全重複**：直接移除，保留最新版本
- **相似代碼**：提取為共用函數
- **函數重複**：更新所有引用指向保留版本

### 品質檢查項目

- 函數長度（預設 50 行）
- 類別大小（預設 200 行）
- 圈複雜度（預設 10）
- 嵌套深度（預設 4 層）
- 參數數量（預設 5 個）
- 安全問題（eval、exec、shell 注入等）

## 📊 輸出示例

```
🏠 專案根目錄: /path/to/project
🔍 開始專案分析...

📊 分析結果:
   📏 發現 3 個超大檔案:
      - src/main.py (752 行)
      - src/utils.py (623 行)
      - src/parser.py (545 行)
   🔄 發現 2 組重複代碼
   🔧 發現 5 個導入優化機會
   ⭐ 代碼品質評分: 73.2/100
   ⚠️  發現 12 個品質問題

🎯 執行建議的操作...
   ✂️  拆分 3 個超大檔案...
   ✅ src/main.py -> 4 個檔案
   ✅ src/utils.py -> 3 個檔案
   ✅ src/parser.py -> 2 個檔案
```

## ⚡ 高級功能

### 1. 專案類型檢測

系統會自動檢測專案類型並應用相應配置：
- Django：允許更大的 models.py、views.py
- Flask：針對路由檔案優化
- Data Science：放寬筆記本轉換檔案的限制

### 2. 語言特定規則

每種語言都有特定的分析規則：
- Python：PEP 8 規範、type hints 檢查
- JavaScript：ES6+ 最佳實踐
- TypeScript：型別安全檢查

### 3. 安全模式

所有重構操作都在安全模式下執行：
- 變更前自動創建快照
- 語法和功能驗證
- 失敗時自動回滾建議

### 4. 實驗性功能

```yaml
experimental:
  enabled: true
  features:
    - "ai_powered_refactoring"      # AI 驅動的重構建議
    - "smart_variable_naming"       # 智能變數命名
    - "auto_documentation_generation" # 自動生成文檔
```

## 🔒 安全性

- **快照備份**：每次操作前自動備份
- **語法驗證**：確保變更後代碼可執行
- **測試驗證**：運行測試確保功能完整
- **回滾機制**：一鍵恢復任何歷史狀態
- **安全模式**：防止誤刪重要代碼

## 📈 性能優化

- **並行處理**：多檔案同時分析
- **智能快取**：避免重複分析
- **增量分析**：只處理變更的檔案
- **記憶體管理**：大檔案分塊處理

## 🤝 整合支援

### Git Hooks
```bash
# 安裝 pre-commit hook
python .claude/run_hooks.py --install-git-hooks
```

### IDE 整合
- VS Code 擴展支援
- PyCharm 插件

### CI/CD
- GitHub Actions 工作流
- GitLab CI 配置

## 📝 命令參考

| 命令 | 說明 |
|------|------|
| `--init` | 初始化配置檔案 |
| `--analyze` | 分析專案或指定檔案 |
| `--split` | 拆分超大檔案 |
| `--clean` | 清理重複代碼 |
| `--optimize` | 優化導入語句 |
| `--validate` | 驗證專案狀態 |
| `--dry-run` | 乾跑模式（僅顯示建議） |
| `--files` | 指定要處理的檔案 |

## 🔍 故障排除

### 常見問題

1. **測試命令未找到**
   ```yaml
   validation:
     test_command: "python -m pytest tests/"
   ```

2. **檔案拆分失敗**
   - 檢查檔案是否有語法錯誤
   - 確認 import 依賴正確

3. **配置無效**
   ```bash
   python .claude/run_hooks.py --validate-config
   ```

### 調試模式

```yaml
debug:
  enabled: true
  verbose_logging: true
  save_intermediate_results: true
```

## 📄 授權

本專案遵循 MIT 授權條款。

## 🙏 貢獻

歡迎提交 Issue 和 Pull Request 來改進這個工具！

---

**Claude Hooks - 讓您的代碼更清潔、更高效、更可維護！** 🚀